# Swagger OIDC Authentication Troubleshooting

## Issue: Swagger doesn't show authentication flow

If you don't see the "Authorize" button or authentication options in Swagger UI, follow these troubleshooting steps:

### 1. Check Application Startup

Ensure the application is running correctly:
```bash
dotnet run --project Uni.Personal.Api
```

The application should start on `http://localhost:5255` and show logs like:
```
Now listening on: http://localhost:5255
Application started. Press Ctrl+C to shut down.
```

### 2. Verify Swagger UI Access

Navigate to: `http://localhost:5255/swagger`

You should see:
- Swagger UI interface
- "Authorize" button in the top-right corner
- API endpoints listed

### 3. Check Swagger Configuration

Verify the configuration in `Program.cs`:

```csharp
// OIDC Security Definition
c.AddSecurityDefinition("oidc", new OpenApiSecurityScheme
{
    Type = SecuritySchemeType.OpenIdConnect,
    OpenIdConnectUrl = new Uri($"{jwtSettings["Authority"]}/.well-known/openid_configuration")
});

// Bearer Security Definition  
c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
{
    Type = SecuritySchemeType.ApiKey,
    Name = "Authorization",
    In = ParameterLocation.Header,
    Scheme = "Bearer"
});
```

### 4. Verify Configuration Files

Check `appsettings.Development.json`:
```json
{
  "Jwt": {
    "Authority": "https://idp-dev.unicloudgroup.com.vn/realms/realm_unipersonal",
    "Audience": "unipersonal"
  },
  "Swagger": {
    "OidcClientId": "swagger_developer",
    "OidcRedirectUri": "http://localhost:5255/swagger/oauth2-redirect.html"
  }
}
```

### 5. Test Authentication Flow

1. **Click "Authorize" button** in Swagger UI
2. **Select authentication method**:
   - `oidc` - for OpenID Connect flow
   - `Bearer` - for manual JWT token input

3. **For OIDC flow**:
   - Click "Authorize" next to `oidc`
   - Should redirect to Keycloak login
   - Login with credentials
   - Should redirect back to Swagger

4. **For Bearer flow**:
   - Click "Authorize" next to `Bearer`
   - Enter JWT token: `Bearer your_jwt_token_here`
   - Click "Authorize"

### 6. Common Issues and Solutions

#### Issue: "Authorize" button not visible
**Solution**: Check browser console for JavaScript errors. Ensure Swagger UI is loading correctly.

#### Issue: OIDC redirect fails
**Solutions**:
- Verify Keycloak client configuration
- Check redirect URI matches exactly: `http://localhost:5255/swagger/oauth2-redirect.html`
- Ensure client is enabled in Keycloak

#### Issue: Bearer authentication not working
**Solutions**:
- Verify JWT token is valid and not expired
- Check token format: must start with "Bearer "
- Ensure token was issued by correct Keycloak realm

#### Issue: 401 Unauthorized after authentication
**Solutions**:
- Check JWT token claims (audience, issuer)
- Verify token has required scopes
- Check controller authorization requirements

### 7. Keycloak Client Configuration

Ensure your Keycloak client (`swagger_developer`) has:

**Basic Settings**:
- Client Type: Public
- Standard Flow: Enabled
- Direct Access Grants: Enabled

**Access Settings**:
- Valid Redirect URIs: `http://localhost:5255/swagger/oauth2-redirect.html`
- Web Origins: `http://localhost:5255`

**Advanced Settings**:
- Access Token Lifespan: Appropriate value (e.g., 15 minutes)

### 8. Testing with HTTP Requests

Use the provided `Uni.Personal.Api.http` file to test authentication:

```http
# Test without authentication (should return 401)
GET http://localhost:5255/v1/api/Order/GetPage

# Test with Bearer token
GET http://localhost:5255/v1/api/Order/GetPage
Authorization: Bearer your_jwt_token_here
```

### 9. Debug Information

Check application logs for authentication-related errors:
- JWT validation errors
- OIDC configuration issues
- Authorization failures

### 10. Browser Developer Tools

1. Open browser developer tools (F12)
2. Check Console tab for JavaScript errors
3. Check Network tab for failed requests
4. Look for CORS issues or blocked requests

## Need Help?

If you're still experiencing issues:

1. Check the application logs for detailed error messages
2. Verify Keycloak server is accessible
3. Test JWT token validation manually
4. Ensure all configuration values are correct
5. Try clearing browser cache and cookies
