
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using Uni.Personal.BLL;
using Uni.Personal.DAL;
using UNI.Common.CommonBase;

namespace Uni.Personal.Api
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddScoped<IUniCommonBaseRepository, UniCommonBaseRepository>();
            builder.Services.RegisterServices();
            builder.Services.RegisterRepositories();

            // Configure JWT Authentication
            var jwtSettings = builder.Configuration.GetSection("Jwt");
            var swaggerSettings = builder.Configuration.GetSection("Swagger");
            builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = jwtSettings["Authority"];
                    options.Audience = jwtSettings["Audience"];
                    options.RequireHttpsMetadata = jwtSettings.GetValue<bool>("RequireHttpsMetadata");

                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = jwtSettings.GetValue<bool>("ValidateIssuer"),
                        ValidateAudience = jwtSettings.GetValue<bool>("ValidateAudience"),
                        ValidateLifetime = jwtSettings.GetValue<bool>("ValidateLifetime"),
                        ValidateIssuerSigningKey = true,
                        ClockSkew = TimeSpan.Zero
                    };
                });

            builder.Services.AddAuthorization();

            builder.Services.AddControllers();
            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Uni Personal Portal API", Version = "v1" });

                // Add OIDC Authentication to Swagger
                c.AddSecurityDefinition("oidc", new OpenApiSecurityScheme
                {
                    Type = SecuritySchemeType.OpenIdConnect,
                    OpenIdConnectUrl = new Uri($"{jwtSettings["Authority"]}/.well-known/openid_configuration")
                });

                // Add JWT Bearer as fallback for manual token input
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer"
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oidc"
                            }
                        },
                        Array.Empty<string>()
                    }
                });
            });


            builder.Host.UseSerilog((context, services, configuration) =>
            {
                configuration.ReadFrom.Configuration(context.Configuration)
                    .ReadFrom.Services(services)
                    .Enrich.FromLogContext();
                //.WriteTo.Async(a =>
                //{
                //    //a.Console();
                //    //a.File("logs/log-.txt");
                //});
            });



            var app = builder.Build();

            // Error handler
            app.UseExceptionHandler(handler =>
            {
                handler.Run(async context =>
                {
                    var exception = context.Features.Get<IExceptionHandlerFeature>()?.Error;
                    context.Response.StatusCode = 500; // Internal Server Error
                    context.Response.ContentType = "application/json";
                    var errorResponse = new
                    {
                        StatusCode = 500,
                        Message = "An unexpected error occurred. Please try again later."
                    };
                    await context.Response.WriteAsJsonAsync(errorResponse);
                });
            });

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Uni Personal Portal API v1");

                    // Configure OIDC for Swagger UI
                    c.OAuthClientId(jwtSettings["Audience"]); // Use the audience as client ID
                    c.OAuthAppName("Uni Personal Portal API");
                    c.OAuthUsePkce();
                    c.OAuthScopeSeparator(" ");
                    c.OAuthScopes("openid", "profile", "email");
                });
            }

            app.UseHttpsRedirection();

            app.UseAuthentication();
            app.UseAuthorization();


            app.MapControllers();

            app.Run();
        }
    }
}
