# Keycloak JWT Authentication Setup

This document describes the JWT authentication configuration for the Uni Personal Portal API.

## Overview

The API has been configured to use Keycloak JWT authentication with the following features:
- JWT Bearer token authentication
- Keycloak integration for token validation
- Swagger UI integration for testing with JWT tokens
- Authorization protection on API endpoints

## Configuration

### JWT Settings

The JWT configuration is stored in `appsettings.json` and `appsettings.Development.json`:

```json
{
  "Jwt": {
    "Authority": "https://idp.unicloudgroup.com.vn/realms/realm_unipersonal",
    "Audience": "unipersonal",
    "RequireHttpsMetadata": true,
    "ValidateAudience": true,
    "ValidateIssuer": true,
    "ValidateLifetime": true
  }
}
```

### Environment-Specific Settings

- **Development**: `RequireHttpsMetadata` is set to `false` for local development
- **Production**: `RequireHttpsMetadata` is set to `true` for security

## Authentication Flow

1. **Client Authentication**: Clients must obtain a JWT token from Keycloak
2. **Token Validation**: The API validates the token against the Keycloak authority
3. **Claims Extraction**: User information is extracted from JWT claims
4. **Authorization**: Protected endpoints require valid authentication

## Protected Endpoints

All endpoints in the `OrderController` are protected with the `[Authorize]` attribute:

- `GET /v1/api/Order/GetPage` - Get paginated orders
- `GET /v1/api/Order/GetInfo` - Get specific order
- `POST /v1/api/Order/SetInfo` - Create/update order
- `DELETE /v1/api/Order/Delete` - Delete order

## Testing with Swagger

1. Start the application in development mode
2. Navigate to the Swagger UI (typically `https://localhost:5001/swagger`)
3. Click the "Authorize" button
4. Enter your JWT token in the format: `Bearer your_jwt_token_here`
5. Test the protected endpoints

## Testing with HTTP Client

Use the provided `Uni.Personal.Api.http` file:

1. Replace `your_jwt_token_here` with a valid JWT token
2. Run the HTTP requests to test authentication

## User Claims Available

The `UniController` base class provides access to the following user information:

- `UserId` - User identifier from JWT claims
- `UserName` - User name from JWT claims
- `ClientId` - Client identifier from JWT claims
- `ProductCode` - Product code from JWT claims

## Obtaining JWT Tokens

To obtain JWT tokens for testing:

1. **Development Environment**: Use the Keycloak development instance at `https://idp-dev.unicloudgroup.com.vn`
2. **Production Environment**: Use the Keycloak production instance at `https://idp.unicloudgroup.com.vn`

### Example Token Request

```bash
curl -X POST \
  'https://idp-dev.unicloudgroup.com.vn/realms/realm_unipersonal/protocol/openid-connect/token' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'grant_type=password&client_id=unipersonal&username=your_username&password=your_password'
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: 
   - Check if JWT token is valid and not expired
   - Verify the token is properly formatted with "Bearer " prefix
   - Ensure the token was issued by the correct Keycloak realm

2. **403 Forbidden**:
   - Check if the user has the required permissions
   - Verify the audience claim in the token matches the configured audience

3. **Token Validation Errors**:
   - Check if the Keycloak authority URL is accessible
   - Verify the realm name in the authority URL
   - Ensure the token signing key is valid

### Logs

Check the application logs for detailed authentication errors. The application uses Serilog for logging.

## Security Considerations

1. **HTTPS**: Always use HTTPS in production environments
2. **Token Expiration**: JWT tokens have a limited lifetime for security
3. **Token Storage**: Store tokens securely on the client side
4. **Refresh Tokens**: Implement token refresh mechanism for long-running applications

## Next Steps

1. **Role-Based Authorization**: Implement role-based access control using JWT claims
2. **API Scopes**: Configure API scopes for fine-grained access control
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Audit Logging**: Add audit logging for authentication events
