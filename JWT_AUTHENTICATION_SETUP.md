# Keycloak JWT Authentication Setup

This document describes the JWT authentication configuration for the Uni Personal Portal API.

## Overview

The API has been configured to use Keycloak JWT authentication with the following features:
- JWT Bearer token authentication
- Keycloak integration for token validation
- Swagger UI integration for testing with JWT tokens
- Authorization protection on API endpoints

## Configuration

### JWT Settings

The JWT configuration is stored in `appsettings.json` and `appsettings.Development.json`:

```json
{
  "Jwt": {
    "Authority": "https://idp.unicloudgroup.com.vn/realms/realm_unipersonal",
    "Audience": "unipersonal",
    "RequireHttpsMetadata": true,
    "ValidateAudience": true,
    "ValidateIssuer": true,
    "ValidateLifetime": true
  }
}
```

### Environment-Specific Settings

- **Development**: `RequireHttpsMetadata` is set to `false` for local development
- **Production**: `RequireHttpsMetadata` is set to `true` for security

### Swagger OIDC Configuration

The Swagger UI is configured with OIDC support for seamless authentication:

```json
{
  "Swagger": {
    "OidcClientId": "unipersonal-swagger",
    "OidcClientSecret": "",
    "OidcScopes": ["openid", "profile", "email"],
    "OidcRedirectUri": "https://localhost:5001/swagger/oauth2-redirect.html"
  }
}
```

**Important**: You need to configure a client named `unipersonal-swagger` in your Keycloak realm with:
- Client Type: Public or Confidential
- Valid Redirect URIs: `https://localhost:5001/swagger/oauth2-redirect.html` (development)
- Valid Redirect URIs: `https://your-production-domain.com/swagger/oauth2-redirect.html` (production)
- Web Origins: `https://localhost:5001` (development) or your production domain

## Authentication Flow

1. **Client Authentication**: Clients must obtain a JWT token from Keycloak
2. **Token Validation**: The API validates the token against the Keycloak authority
3. **Claims Extraction**: User information is extracted from JWT claims
4. **Authorization**: Protected endpoints require valid authentication

## Protected Endpoints

All endpoints in the `OrderController` are protected with the `[Authorize]` attribute:

- `GET /v1/api/Order/GetPage` - Get paginated orders
- `GET /v1/api/Order/GetInfo` - Get specific order
- `POST /v1/api/Order/SetInfo` - Create/update order
- `DELETE /v1/api/Order/Delete` - Delete order

## Testing with Swagger

### Option 1: OIDC Authentication (Recommended)

1. Start the application in development mode
2. Navigate to the Swagger UI (typically `https://localhost:5001/swagger`)
3. Click the "Authorize" button
4. Select "oidc" authentication scheme
5. Click "Authorize" to be redirected to Keycloak login
6. Login with your Keycloak credentials
7. You'll be redirected back to Swagger with authentication
8. Test the protected endpoints

### Option 2: Manual JWT Token

1. Start the application in development mode
2. Navigate to the Swagger UI (typically `https://localhost:5001/swagger`)
3. Click the "Authorize" button
4. Select "Bearer" authentication scheme
5. Enter your JWT token in the format: `Bearer your_jwt_token_here`
6. Test the protected endpoints

## Testing with HTTP Client

Use the provided `Uni.Personal.Api.http` file:

1. Replace `your_jwt_token_here` with a valid JWT token
2. Run the HTTP requests to test authentication

## User Claims Available

The `UniController` base class provides access to the following user information:

- `UserId` - User identifier from JWT claims
- `UserName` - User name from JWT claims
- `ClientId` - Client identifier from JWT claims
- `ProductCode` - Product code from JWT claims

## Obtaining JWT Tokens

To obtain JWT tokens for testing:

1. **Development Environment**: Use the Keycloak development instance at `https://idp-dev.unicloudgroup.com.vn`
2. **Production Environment**: Use the Keycloak production instance at `https://idp.unicloudgroup.com.vn`

### Example Token Request

```bash
curl -X POST \
  'https://idp-dev.unicloudgroup.com.vn/realms/realm_unipersonal/protocol/openid-connect/token' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'grant_type=password&client_id=unipersonal&username=your_username&password=your_password'
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**:
   - Check if JWT token is valid and not expired
   - Verify the token is properly formatted with "Bearer " prefix
   - Ensure the token was issued by the correct Keycloak realm

2. **403 Forbidden**:
   - Check if the user has the required permissions
   - Verify the audience claim in the token matches the configured audience

3. **Token Validation Errors**:
   - Check if the Keycloak authority URL is accessible
   - Verify the realm name in the authority URL
   - Ensure the token signing key is valid

### Logs

Check the application logs for detailed authentication errors. The application uses Serilog for logging.

## Security Considerations

1. **HTTPS**: Always use HTTPS in production environments
2. **Token Expiration**: JWT tokens have a limited lifetime for security
3. **Token Storage**: Store tokens securely on the client side
4. **Refresh Tokens**: Implement token refresh mechanism for long-running applications

## Keycloak Client Configuration

To use the OIDC authentication in Swagger, you need to configure a client in Keycloak:

### Creating the Swagger Client

1. **Login to Keycloak Admin Console**
2. **Navigate to your realm** (`realm_unipersonal`)
3. **Go to Clients** → **Create Client**
4. **Configure the client**:
   - Client ID: `unipersonal-swagger`
   - Client Type: `Public` (for development) or `Confidential` (for production)
   - Standard Flow: `Enabled`
   - Direct Access Grants: `Enabled`

5. **Set Valid Redirect URIs**:
   - Development: `https://localhost:5001/swagger/oauth2-redirect.html`
   - Production: `https://your-production-domain.com/swagger/oauth2-redirect.html`

6. **Set Web Origins**:
   - Development: `https://localhost:5001`
   - Production: `https://your-production-domain.com`

7. **Configure Client Scopes** (Optional):
   - Add `openid`, `profile`, `email` scopes

### Testing the Configuration

1. Start your API application
2. Navigate to Swagger UI
3. Click "Authorize" and select "oidc"
4. You should be redirected to Keycloak login
5. After successful login, you'll be redirected back to Swagger

## Next Steps

1. **Role-Based Authorization**: Implement role-based access control using JWT claims
2. **API Scopes**: Configure API scopes for fine-grained access control
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Audit Logging**: Add audit logging for authentication events
5. **Production Deployment**: Update redirect URIs for production environment
